/**
 * KFT Fitness Admin Dashboard - Responsive Navigation
 * Handles mobile bottom navigation, sidebar, and responsive behavior
 */

class ResponsiveNavigation {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.handleResize();
        this.initializeActiveStates();
        
        // Listen for window resize
        window.addEventListener('resize', () => this.handleResize());
        
        // Handle page visibility changes for mobile optimization
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
    }

    bindEvents() {
        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => this.toggleMobileSidebar());
        }

        // Mobile sidebar close
        const mobileSidebarClose = document.getElementById('mobileSidebarClose');
        if (mobileSidebarClose) {
            mobileSidebarClose.addEventListener('click', () => this.closeMobileSidebar());
        }

        // Sidebar overlay
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => this.closeMobileSidebar());
        }

        // Mobile "More" button
        const mobileMoreToggle = document.getElementById('mobileMoreToggle');
        if (mobileMoreToggle) {
            mobileMoreToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.showMoreModal();
            });
        }

        // Handle mobile navigation item clicks
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item:not(#mobileMoreToggle)');
        mobileNavItems.forEach(item => {
            item.addEventListener('click', () => this.handleMobileNavClick(item));
        });

        // Handle sidebar item clicks
        const sidebarItems = document.querySelectorAll('.mobile-sidebar-item, .desktop-sidebar-item');
        sidebarItems.forEach(item => {
            item.addEventListener('click', () => this.handleSidebarClick(item));
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeMobileSidebar();
            }
        });

        // Handle swipe gestures on mobile
        this.initializeSwipeGestures();
    }

    toggleMobileSidebar() {
        const sidebar = document.getElementById('mobileSidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        if (sidebar && overlay) {
            const isActive = sidebar.classList.contains('active');
            
            if (isActive) {
                this.closeMobileSidebar();
            } else {
                this.openMobileSidebar();
            }
        }
    }

    openMobileSidebar() {
        const sidebar = document.getElementById('mobileSidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Add animation class
            sidebar.style.transform = 'translateX(0)';
        }
    }

    closeMobileSidebar() {
        const sidebar = document.getElementById('mobileSidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
            
            // Reset transform
            sidebar.style.transform = '';
        }
    }

    showMoreModal() {
        const modal = new bootstrap.Modal(document.getElementById('mobileMoreModal'));
        modal.show();
    }

    handleMobileNavClick(item) {
        // Add visual feedback
        item.classList.add('clicked');
        setTimeout(() => item.classList.remove('clicked'), 200);
        
        // Store navigation state
        this.storeNavigationState(item.href);
    }

    handleSidebarClick(item) {
        // Close mobile sidebar if open
        this.closeMobileSidebar();
        
        // Add visual feedback
        item.classList.add('clicked');
        setTimeout(() => item.classList.remove('clicked'), 200);
        
        // Store navigation state
        this.storeNavigationState(item.href);
    }

    handleResize() {
        const width = window.innerWidth;
        
        // Close mobile sidebar on resize to desktop
        if (width >= 768) {
            this.closeMobileSidebar();
        }
        
        // Update viewport height for mobile browsers
        if (width < 768) {
            this.updateMobileViewport();
        }
    }

    updateMobileViewport() {
        // Fix for mobile browsers with dynamic viewport height
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden, pause any animations or timers
            this.pauseAnimations();
        } else {
            // Page is visible, resume animations
            this.resumeAnimations();
        }
    }

    pauseAnimations() {
        // Pause any running animations for performance
        const animatedElements = document.querySelectorAll('.animated');
        animatedElements.forEach(el => {
            el.style.animationPlayState = 'paused';
        });
    }

    resumeAnimations() {
        // Resume animations
        const animatedElements = document.querySelectorAll('.animated');
        animatedElements.forEach(el => {
            el.style.animationPlayState = 'running';
        });
    }

    initializeActiveStates() {
        const currentPath = window.location.pathname;
        const currentPage = currentPath.split('/').pop();
        
        // Set active states for navigation items
        const navItems = document.querySelectorAll('.mobile-nav-item, .mobile-sidebar-item, .desktop-sidebar-item');
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && href !== '#') {
                const itemPage = href.split('/').pop();
                if (itemPage === currentPage) {
                    item.classList.add('active');
                }
            }
        });
    }

    storeNavigationState(url) {
        // Store navigation state for analytics or user experience
        if (url && url !== '#') {
            sessionStorage.setItem('lastNavigation', JSON.stringify({
                url: url,
                timestamp: Date.now(),
                userAgent: navigator.userAgent.includes('Mobile') ? 'mobile' : 'desktop'
            }));
        }
    }

    initializeSwipeGestures() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            this.handleSwipe();
        });

        const handleSwipe = () => {
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const minSwipeDistance = 50;

            // Only handle horizontal swipes
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
                if (deltaX > 0 && startX < 50) {
                    // Swipe right from left edge - open sidebar
                    this.openMobileSidebar();
                } else if (deltaX < 0) {
                    // Swipe left - close sidebar
                    this.closeMobileSidebar();
                }
            }
        };

        this.handleSwipe = handleSwipe;
    }

    // Public methods for external use
    static getInstance() {
        if (!ResponsiveNavigation.instance) {
            ResponsiveNavigation.instance = new ResponsiveNavigation();
        }
        return ResponsiveNavigation.instance;
    }

    // Utility method to check if mobile
    static isMobile() {
        return window.innerWidth < 768;
    }

    // Utility method to check if tablet
    static isTablet() {
        return window.innerWidth >= 768 && window.innerWidth < 992;
    }

    // Utility method to check if desktop
    static isDesktop() {
        return window.innerWidth >= 992;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    ResponsiveNavigation.getInstance();
});

// Add CSS for click animations
const style = document.createElement('style');
style.textContent = `
    .mobile-nav-item.clicked,
    .mobile-sidebar-item.clicked,
    .desktop-sidebar-item.clicked {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
    
    .mobile-nav-item {
        transition: all 0.2s ease;
    }
    
    .mobile-nav-item:active {
        transform: scale(0.95);
    }
    
    /* Fix for mobile viewport height */
    .mobile-bottom-nav {
        height: calc(var(--mobile-bottom-nav-height) + env(safe-area-inset-bottom));
        padding-bottom: env(safe-area-inset-bottom);
    }
    
    .main-content {
        margin-bottom: calc(var(--mobile-bottom-nav-height) + env(safe-area-inset-bottom));
    }
    
    /* Smooth transitions for responsive changes */
    .responsive-card,
    .stats-card {
        transition: all 0.3s ease;
    }
    
    /* Loading states */
    .nav-loading {
        opacity: 0.6;
        pointer-events: none;
    }
`;
document.head.appendChild(style);

// Export for use in other scripts
window.ResponsiveNavigation = ResponsiveNavigation;
