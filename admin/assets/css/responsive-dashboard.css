/* KFT Fitness Admin Dashboard - Fully Responsive Design */
/* Mobile-First Responsive Design with Bottom Navigation */

:root {
  /* Color System */
  --primary-color: #111;
  --secondary-color: #888;
  --success-color: #27ae60;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --info-color: #3498db;
  
  /* Background Colors */
  --bg-primary: #fff;
  --bg-secondary: #f5f7fa;
  --bg-dark: #111;
  --bg-light: #f8f9fa;
  
  /* Text Colors */
  --text-primary: #111;
  --text-secondary: #666;
  --text-muted: #888;
  --text-light: #fff;
  
  /* Spacing System */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.25s ease;
  --transition-slow: 0.35s ease;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Mobile Navigation */
  --mobile-nav-height: 70px;
  --mobile-bottom-nav-height: 80px;
  --sidebar-width: 280px;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Mobile-First Layout Container */
.responsive-container {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Mobile-First Grid System */
.responsive-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

/* Mobile Navigation Styles */
.mobile-top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--mobile-nav-height);
  background: var(--bg-primary);
  border-bottom: 1px solid #e0e0e0;
  z-index: var(--z-fixed);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.mobile-nav-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
}

.mobile-nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.mobile-nav-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: var(--transition-fast);
  cursor: pointer;
}

.mobile-nav-btn:hover {
  background: var(--bg-light);
  color: var(--text-primary);
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--mobile-bottom-nav-height);
  background: var(--bg-primary);
  border-top: 1px solid #e0e0e0;
  z-index: var(--z-fixed);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: var(--spacing-sm) 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--text-secondary);
  transition: var(--transition-fast);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  min-width: 60px;
  position: relative;
}

.mobile-nav-item.active {
  color: var(--primary-color);
}

.mobile-nav-item:hover {
  color: var(--primary-color);
  background: var(--bg-light);
}

.mobile-nav-icon {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xs);
}

.mobile-nav-label {
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
}

/* Main Content Area */
.main-content {
  margin-top: var(--mobile-nav-height);
  margin-bottom: var(--mobile-bottom-nav-height);
  padding: var(--spacing-md);
  min-height: calc(100vh - var(--mobile-nav-height) - var(--mobile-bottom-nav-height));
}

/* Responsive Cards */
.responsive-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  border: 1px solid #e0e0e0;
  transition: var(--transition-base);
}

.responsive-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.responsive-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid #e0e0e0;
}

.responsive-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.responsive-card-body {
  padding: 0;
}

/* Stats Cards */
.stats-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  border: 1px solid #e0e0e0;
  transition: var(--transition-base);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-color);
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  font-size: 1.25rem;
}

.stats-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stats-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Responsive Tables */
.responsive-table-container {
  overflow-x: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  background: var(--bg-primary);
}

.responsive-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.responsive-table th,
.responsive-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.responsive-table th {
  background: var(--bg-light);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.responsive-table tbody tr:hover {
  background: var(--bg-light);
}

/* Touch-Friendly Buttons */
.touch-btn {
  min-height: 44px;
  min-width: 44px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  transition: var(--transition-fast);
  cursor: pointer;
  font-size: 0.875rem;
}

.touch-btn-primary {
  background: var(--primary-color);
  color: var(--text-light);
}

.touch-btn-primary:hover {
  background: #000;
  color: var(--text-light);
}

.touch-btn-success {
  background: var(--success-color);
  color: var(--text-light);
}

.touch-btn-success:hover {
  background: #219150;
  color: var(--text-light);
}

.touch-btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid #e0e0e0;
}

.touch-btn-outline:hover {
  background: var(--bg-light);
  color: var(--text-primary);
}

/* Form Controls */
.responsive-form-control {
  width: 100%;
  min-height: 44px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-fast);
  background: var(--bg-primary);
}

.responsive-form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(17, 17, 17, 0.1);
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-base);
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-sidebar {
  position: fixed;
  top: 0;
  left: -100%;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--bg-dark);
  z-index: var(--z-modal);
  transition: var(--transition-base);
  overflow-y: auto;
}

.mobile-sidebar.active {
  left: 0;
}

.mobile-sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-sidebar-title {
  color: var(--text-light);
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.mobile-sidebar-close {
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-xs);
}

.mobile-sidebar-menu {
  padding: var(--spacing-md) 0;
}

.mobile-sidebar-section {
  margin-bottom: var(--spacing-lg);
}

.mobile-sidebar-section-header {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 0.75rem;
  font-weight: 600;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mobile-sidebar-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  color: #ccc;
  text-decoration: none;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.mobile-sidebar-item:hover {
  background: #222;
  color: var(--text-light);
  border-left-color: var(--success-color);
}

.mobile-sidebar-item.active {
  background: #222;
  color: var(--text-light);
  border-left-color: var(--success-color);
}

.mobile-sidebar-item i {
  width: 20px;
  margin-right: var(--spacing-md);
  text-align: center;
}

/* Utility Classes */
.hide-mobile {
  display: none;
}

.show-mobile {
  display: block;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .responsive-container {
    padding: 0 var(--spacing-lg);
  }

  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-card {
    padding: var(--spacing-xl);
  }

  .stats-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .stats-value {
    font-size: 2.5rem;
  }

  .hide-mobile {
    display: block;
  }

  .show-mobile {
    display: none;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .responsive-grid.grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .main-content {
    padding: var(--spacing-lg);
  }

  .mobile-bottom-nav {
    display: none;
  }

  .mobile-top-nav {
    display: none;
  }

  /* Show desktop sidebar */
  .desktop-sidebar {
    display: block;
  }

  .main-content {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: var(--sidebar-width);
    padding: var(--spacing-xl);
  }

  .responsive-card {
    padding: var(--spacing-xl);
  }

  .responsive-table th,
  .responsive-table td {
    padding: var(--spacing-lg);
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .responsive-container {
    max-width: 1200px;
    padding: 0 var(--spacing-xl);
  }

  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .responsive-grid.grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .main-content {
    padding: var(--spacing-xxl);
  }

  .stats-card {
    padding: var(--spacing-xxl);
  }

  .stats-icon {
    width: 70px;
    height: 70px;
    font-size: 1.75rem;
  }

  .stats-value {
    font-size: 3rem;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .responsive-container {
    max-width: 1400px;
  }

  .responsive-grid.grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .responsive-grid.grid-5 {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Desktop Sidebar Styles */
.desktop-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--bg-dark);
  z-index: var(--z-fixed);
  overflow-y: auto;
  display: none;
  transition: var(--transition-base);
}

.desktop-sidebar-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid #333;
}

.desktop-sidebar-title {
  color: var(--text-light);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.desktop-sidebar-menu {
  padding: var(--spacing-lg) 0;
}

.desktop-sidebar-section {
  margin-bottom: var(--spacing-xl);
}

.desktop-sidebar-section-header {
  padding: var(--spacing-sm) var(--spacing-xl);
  font-size: 0.75rem;
  font-weight: 600;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.desktop-sidebar-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  color: #ccc;
  text-decoration: none;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.desktop-sidebar-item:hover {
  background: #222;
  color: var(--text-light);
  border-left-color: var(--success-color);
}

.desktop-sidebar-item.active {
  background: #222;
  color: var(--text-light);
  border-left-color: var(--success-color);
}

.desktop-sidebar-item i {
  width: 20px;
  margin-right: var(--spacing-md);
  text-align: center;
}

/* Desktop Top Navigation */
.desktop-top-nav {
  position: fixed;
  top: 0;
  left: var(--sidebar-width);
  right: 0;
  height: var(--mobile-nav-height);
  background: var(--bg-primary);
  border-bottom: 1px solid #e0e0e0;
  z-index: var(--z-sticky);
  display: none;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

@media (min-width: 768px) {
  .desktop-top-nav {
    display: flex;
  }

  .main-content {
    margin-top: var(--mobile-nav-height);
  }
}
