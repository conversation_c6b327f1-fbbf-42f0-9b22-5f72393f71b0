<?php
/**
 * Responsive Navigation Component
 * Handles both mobile bottom navigation and desktop sidebar
 */

// Get current page for active state
$currentPage = basename($_SERVER['PHP_SELF']);

// Define navigation items with their priorities for mobile
$navigationItems = [
    // High priority items (shown in mobile bottom nav)
    'dashboard' => [
        'url' => 'index.php',
        'icon' => 'fas fa-tachometer-alt',
        'label' => 'Dashboard',
        'mobile_priority' => 1,
        'permission' => null
    ],
    'users' => [
        'url' => 'users.php',
        'icon' => 'fas fa-users',
        'label' => 'Users',
        'mobile_priority' => 2,
        'permission' => 'manage_users'
    ],
    'courses' => [
        'url' => 'courses.php',
        'icon' => 'fas fa-graduation-cap',
        'label' => 'Courses',
        'mobile_priority' => 3,
        'permission' => 'manage_courses'
    ],
    'reports' => [
        'url' => 'reports.php',
        'icon' => 'fas fa-chart-bar',
        'label' => 'Reports',
        'mobile_priority' => 4,
        'permission' => 'view_reports'
    ],
    'more' => [
        'url' => '#',
        'icon' => 'fas fa-ellipsis-h',
        'label' => 'More',
        'mobile_priority' => 5,
        'permission' => null,
        'is_dropdown' => true
    ],
    
    // Lower priority items (shown in sidebar and "More" dropdown)
    'staff_management' => [
        'url' => 'staff_management.php',
        'icon' => 'fas fa-user-shield',
        'label' => 'Staff Management',
        'mobile_priority' => 6,
        'permission' => 'super_admin'
    ],
    'course_assign' => [
        'url' => 'course_assign.php',
        'icon' => 'fas fa-user-plus',
        'label' => 'Assign Courses',
        'mobile_priority' => 7,
        'permission' => 'assign_users'
    ],
    'water_reminders' => [
        'url' => 'water_reminders.php',
        'icon' => 'fas fa-tint',
        'label' => 'Water Reminders',
        'mobile_priority' => 8,
        'permission' => 'manage_settings'
    ],
    'audit_logs' => [
        'url' => 'audit_logs.php',
        'icon' => 'fas fa-history',
        'label' => 'Audit Logs',
        'mobile_priority' => 9,
        'permission' => 'super_admin'
    ],
    'settings' => [
        'url' => 'settings.php',
        'icon' => 'fas fa-cog',
        'label' => 'Settings',
        'mobile_priority' => 10,
        'permission' => 'manage_settings'
    ]
];

/**
 * Check if user has permission for navigation item
 */
function hasNavigationPermission($item, $auth) {
    if (!isset($item['permission']) || $item['permission'] === null) {
        return true;
    }
    
    if ($item['permission'] === 'super_admin') {
        return $auth->hasRole('super_admin');
    }
    
    if ($item['permission'] === 'manage_users') {
        return $auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('manage_users');
    }
    
    if ($item['permission'] === 'manage_courses') {
        return $auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('manage_courses');
    }
    
    if ($item['permission'] === 'view_reports') {
        return $auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('view_reports');
    }
    
    if ($item['permission'] === 'assign_users') {
        return $auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('assign_users');
    }
    
    if ($item['permission'] === 'manage_settings') {
        return $auth->hasRole('super_admin') || $auth->hasRole('admin') || hasPermission('manage_settings');
    }
    
    return false;
}

/**
 * Get filtered navigation items based on user permissions
 */
function getFilteredNavigationItems($navigationItems, $auth) {
    $filtered = [];
    foreach ($navigationItems as $key => $item) {
        if (hasNavigationPermission($item, $auth)) {
            $filtered[$key] = $item;
        }
    }
    return $filtered;
}

/**
 * Get mobile navigation items (top 4 priority + More)
 */
function getMobileNavigationItems($navigationItems, $auth) {
    $filtered = getFilteredNavigationItems($navigationItems, $auth);
    
    // Sort by mobile priority
    uasort($filtered, function($a, $b) {
        return $a['mobile_priority'] - $b['mobile_priority'];
    });
    
    // Take first 4 items + More button
    $mobileItems = array_slice($filtered, 0, 4, true);
    $mobileItems['more'] = $navigationItems['more'];
    
    return $mobileItems;
}

/**
 * Get items for the "More" dropdown
 */
function getMoreDropdownItems($navigationItems, $auth) {
    $filtered = getFilteredNavigationItems($navigationItems, $auth);
    
    // Sort by mobile priority
    uasort($filtered, function($a, $b) {
        return $a['mobile_priority'] - $b['mobile_priority'];
    });
    
    // Return items after the first 4
    return array_slice($filtered, 4, null, true);
}

/**
 * Check if current page is active
 */
function isActivePage($itemUrl, $currentPage) {
    if ($itemUrl === '#') return false;
    return basename($itemUrl) === $currentPage;
}

// Get filtered items
$filteredItems = getFilteredNavigationItems($navigationItems, $auth);
$mobileItems = getMobileNavigationItems($navigationItems, $auth);
$moreDropdownItems = getMoreDropdownItems($navigationItems, $auth);
?>

<!-- Mobile Top Navigation -->
<div class="mobile-top-nav d-block d-md-none">
    <a href="index.php" class="mobile-nav-brand">
        <?php echo APP_NAME; ?>
    </a>
    <div class="mobile-nav-actions">
        <button class="mobile-nav-btn" id="mobileMenuToggle" aria-label="Open Menu">
            <i class="fas fa-bars"></i>
        </button>
        <a href="notifications.php" class="mobile-nav-btn" aria-label="Notifications">
            <i class="fas fa-bell"></i>
        </a>
        <a href="profile.php" class="mobile-nav-btn" aria-label="Profile">
            <i class="fas fa-user"></i>
        </a>
    </div>
</div>

<!-- Mobile Bottom Navigation -->
<div class="mobile-bottom-nav d-flex d-md-none">
    <?php foreach ($mobileItems as $key => $item): ?>
        <?php if ($key === 'more'): ?>
            <a href="#" class="mobile-nav-item" id="mobileMoreToggle" aria-label="More Options">
                <i class="mobile-nav-icon <?php echo $item['icon']; ?>"></i>
                <span class="mobile-nav-label"><?php echo $item['label']; ?></span>
            </a>
        <?php else: ?>
            <a href="<?php echo $item['url']; ?>" 
               class="mobile-nav-item <?php echo isActivePage($item['url'], $currentPage) ? 'active' : ''; ?>"
               aria-label="<?php echo $item['label']; ?>">
                <i class="mobile-nav-icon <?php echo $item['icon']; ?>"></i>
                <span class="mobile-nav-label"><?php echo $item['label']; ?></span>
            </a>
        <?php endif; ?>
    <?php endforeach; ?>
</div>

<!-- Mobile Sidebar Overlay -->
<div class="sidebar-overlay d-block d-md-none" id="sidebarOverlay"></div>

<!-- Mobile Sidebar -->
<div class="mobile-sidebar d-block d-md-none" id="mobileSidebar">
    <div class="mobile-sidebar-header">
        <h4 class="mobile-sidebar-title"><?php echo APP_NAME; ?></h4>
        <button class="mobile-sidebar-close" id="mobileSidebarClose" aria-label="Close Menu">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="mobile-sidebar-menu">
        <?php
        // Group items by sections for mobile sidebar
        $sections = [
            'DASHBOARD' => ['dashboard'],
            'USER OPERATIONS' => ['users', 'course_assign'],
            'COURSES' => ['courses'],
            'INSIGHTS & REPORTS' => ['reports'],
            'PLATFORM CONTROL' => ['staff_management', 'audit_logs', 'settings'],
            'ENGAGEMENT' => ['water_reminders']
        ];
        
        foreach ($sections as $sectionName => $sectionItems):
            $hasVisibleItems = false;
            foreach ($sectionItems as $itemKey) {
                if (isset($filteredItems[$itemKey])) {
                    $hasVisibleItems = true;
                    break;
                }
            }
            
            if ($hasVisibleItems):
        ?>
            <div class="mobile-sidebar-section">
                <div class="mobile-sidebar-section-header"><?php echo $sectionName; ?></div>
                <?php foreach ($sectionItems as $itemKey): ?>
                    <?php if (isset($filteredItems[$itemKey])): ?>
                        <?php $item = $filteredItems[$itemKey]; ?>
                        <a href="<?php echo $item['url']; ?>" 
                           class="mobile-sidebar-item <?php echo isActivePage($item['url'], $currentPage) ? 'active' : ''; ?>">
                            <i class="<?php echo $item['icon']; ?>"></i>
                            <?php echo $item['label']; ?>
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php 
            endif;
        endforeach; 
        ?>
    </div>
</div>

<!-- Desktop Sidebar (Hidden on Mobile) -->
<div class="desktop-sidebar d-none d-md-block">
    <div class="desktop-sidebar-header">
        <h4 class="desktop-sidebar-title"><?php echo APP_NAME; ?></h4>
    </div>
    <div class="desktop-sidebar-menu">
        <?php foreach ($sections as $sectionName => $sectionItems): ?>
            <?php
            $hasVisibleItems = false;
            foreach ($sectionItems as $itemKey) {
                if (isset($filteredItems[$itemKey])) {
                    $hasVisibleItems = true;
                    break;
                }
            }
            
            if ($hasVisibleItems):
            ?>
                <div class="desktop-sidebar-section">
                    <div class="desktop-sidebar-section-header"><?php echo $sectionName; ?></div>
                    <?php foreach ($sectionItems as $itemKey): ?>
                        <?php if (isset($filteredItems[$itemKey])): ?>
                            <?php $item = $filteredItems[$itemKey]; ?>
                            <a href="<?php echo $item['url']; ?>" 
                               class="desktop-sidebar-item <?php echo isActivePage($item['url'], $currentPage) ? 'active' : ''; ?>">
                                <i class="<?php echo $item['icon']; ?>"></i>
                                <?php echo $item['label']; ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
</div>

<!-- Desktop Top Navigation (Hidden on Mobile) -->
<div class="desktop-top-nav d-none d-md-flex">
    <div class="d-flex align-items-center">
        <!-- Breadcrumb or page title can go here -->
    </div>
    <div class="d-flex align-items-center gap-3">
        <a href="notifications.php" class="mobile-nav-btn" title="Notifications">
            <i class="fas fa-bell"></i>
        </a>
        <div class="dropdown">
            <button class="mobile-nav-btn dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                <i class="fas fa-user"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="logout.php">Logout</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- More Dropdown Modal for Mobile -->
<div class="modal fade" id="mobileMoreModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">More Options</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group list-group-flush">
                    <?php foreach ($moreDropdownItems as $key => $item): ?>
                        <a href="<?php echo $item['url']; ?>" 
                           class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="<?php echo $item['icon']; ?> me-3"></i>
                            <?php echo $item['label']; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
